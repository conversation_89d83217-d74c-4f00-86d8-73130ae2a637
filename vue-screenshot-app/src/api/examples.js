// API使用示例
import { hospitalApi, bookingApi, userApi, systemApi } from '@/api'

/**
 * 医院相关API使用示例
 */
export const hospitalExamples = {
  // 获取医院列表
  async getHospitals() {
    try {
      const hospitals = await hospitalApi.getHospitalList({
        keyword: '人民医院',
        city: '宁波',
        page: 1,
        size: 10
      })
      console.log('医院列表:', hospitals)
      return hospitals
    } catch (error) {
      console.error('获取医院列表失败:', error.message)
    }
  },

  // 搜索医院
  async searchHospitals(keyword) {
    try {
      const results = await hospitalApi.searchHospitals(keyword)
      console.log('搜索结果:', results)
      return results
    } catch (error) {
      console.error('搜索医院失败:', error.message)
    }
  },

  // 获取医院详情
  async getHospitalDetail(id) {
    try {
      const hospital = await hospitalApi.getHospitalDetail(id)
      console.log('医院详情:', hospital)
      return hospital
    } catch (error) {
      console.error('获取医院详情失败:', error.message)
    }
  }
}

/**
 * 预约相关API使用示例
 */
export const bookingExamples = {
  // 创建预约
  async createBooking(formData) {
    try {
      const booking = await bookingApi.createBooking({
        hospitalId: formData.hospitalId,
        patientName: formData.patientName,
        patientPhone: formData.patientPhone,
        location: formData.location,
        serviceTime: formData.serviceTime,
        requirements: formData.requirements
      })
      console.log('预约创建成功:', booking)
      return booking
    } catch (error) {
      console.error('创建预约失败:', error.message)
      throw error
    }
  },

  // 获取我的预约
  async getMyBookings(status = '') {
    try {
      const bookings = await bookingApi.getMyBookings({
        status,
        page: 1,
        size: 20
      })
      console.log('我的预约:', bookings)
      return bookings
    } catch (error) {
      console.error('获取预约列表失败:', error.message)
    }
  },

  // 取消预约
  async cancelBooking(id, reason) {
    try {
      await bookingApi.cancelBooking(id, reason)
      console.log('预约取消成功')
      return true
    } catch (error) {
      console.error('取消预约失败:', error.message)
      throw error
    }
  },

  // 评价预约
  async reviewBooking(id, rating, comment) {
    try {
      await bookingApi.reviewBooking(id, {
        rating,
        comment
      })
      console.log('评价提交成功')
      return true
    } catch (error) {
      console.error('提交评价失败:', error.message)
      throw error
    }
  }
}

/**
 * 用户相关API使用示例
 */
export const userExamples = {
  // 用户登录
  async login(phone, code) {
    try {
      const result = await userApi.login({
        phone,
        code
      })
      
      // 保存token
      localStorage.setItem('token', result.token)
      console.log('登录成功:', result)
      return result
    } catch (error) {
      console.error('登录失败:', error.message)
      throw error
    }
  },

  // 发送验证码
  async sendCode(phone) {
    try {
      await userApi.sendSmsCode(phone)
      console.log('验证码发送成功')
      return true
    } catch (error) {
      console.error('发送验证码失败:', error.message)
      throw error
    }
  },

  // 获取用户信息
  async getUserInfo() {
    try {
      const userInfo = await userApi.getUserInfo()
      console.log('用户信息:', userInfo)
      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error.message)
    }
  },

  // 退出登录
  async logout() {
    try {
      await userApi.logout()
      localStorage.removeItem('token')
      console.log('退出登录成功')
      return true
    } catch (error) {
      console.error('退出登录失败:', error.message)
    }
  }
}

/**
 * 在Vue组件中使用API的示例
 */
export const componentExample = `
<template>
  <div>
    <div v-for="hospital in hospitals" :key="hospital.id">
      {{ hospital.name }}
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      hospitals: []
    }
  },
  
  async mounted() {
    await this.loadHospitals()
  },
  
  methods: {
    // 加载医院列表
    async loadHospitals() {
      try {
        this.hospitals = await this.$api.hospital.getHospitalList()
      } catch (error) {
        this.$message.error('获取医院列表失败: ' + error.message)
      }
    },
    
    // 创建预约
    async createBooking(formData) {
      try {
        await this.$api.booking.createBooking(formData)
        this.$message.success('预约创建成功')
        // 刷新预约列表
        await this.loadMyBookings()
      } catch (error) {
        this.$message.error('创建预约失败: ' + error.message)
      }
    },
    
    // 取消预约
    async cancelBooking(id) {
      try {
        await this.$api.booking.cancelBooking(id, '用户主动取消')
        this.$message.success('预约取消成功')
        // 刷新预约列表
        await this.loadMyBookings()
      } catch (error) {
        this.$message.error('取消预约失败: ' + error.message)
      }
    }
  }
}
</script>
`

/**
 * 在Composition API中使用的示例
 */
export const compositionExample = `
<script setup>
import { ref, onMounted } from 'vue'
import { hospitalApi, bookingApi } from '@/api'

const hospitals = ref([])
const loading = ref(false)

// 加载医院列表
const loadHospitals = async () => {
  try {
    loading.value = true
    hospitals.value = await hospitalApi.getHospitalList()
  } catch (error) {
    console.error('获取医院列表失败:', error.message)
  } finally {
    loading.value = false
  }
}

// 创建预约
const createBooking = async (formData) => {
  try {
    await bookingApi.createBooking(formData)
    console.log('预约创建成功')
  } catch (error) {
    console.error('创建预约失败:', error.message)
  }
}

onMounted(() => {
  loadHospitals()
})
</script>
`
