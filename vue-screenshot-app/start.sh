#!/bin/bash

echo "正在启动医院预约陪诊应用..."
echo ""

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "错误：未找到Node.js，请先安装Node.js"
    exit 1
fi

echo "Node.js版本: $(node --version)"

# 检查npm环境
if ! command -v npm &> /dev/null; then
    echo "错误：未找到npm"
    exit 1
fi

echo "npm版本: $(npm --version)"
echo ""

# 安装项目依赖
echo "安装项目依赖..."
npm install

if [ $? -ne 0 ]; then
    echo "错误：依赖安装失败"
    exit 1
fi

echo ""
echo "启动开发服务器..."
echo "应用将在 http://localhost:8080 打开"
echo ""

# 启动开发服务器
npm run serve
