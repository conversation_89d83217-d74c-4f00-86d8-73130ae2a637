// API统一入口文件
import { hospitalApi, bookingApi, userApi, systemApi } from './hospital'

/**
 * 统一导出所有API接口
 */
export {
  hospitalApi,
  bookingApi,
  userApi,
  systemApi
}

/**
 * 默认导出 - 包含所有API的对象
 */
export default {
  hospital: hospitalApi,
  booking: bookingApi,
  user: userApi,
  system: systemApi
}

/**
 * API使用示例：
 * 
 * 方式1 - 按模块导入
 * import { hospitalApi, bookingApi } from '@/api'
 * const hospitals = await hospitalApi.getHospitalList()
 * const bookings = await bookingApi.getMyBookings()
 * 
 * 方式2 - 默认导入
 * import api from '@/api'
 * const hospitals = await api.hospital.getHospitalList()
 * const bookings = await api.booking.getMyBookings()
 * 
 * 方式3 - 在组件中使用
 * export default {
 *   async mounted() {
 *     try {
 *       const hospitals = await this.$api.hospital.getHospitalList()
 *       this.hospitals = hospitals
 *     } catch (error) {
 *       console.error('获取医院列表失败:', error.message)
 *     }
 *   }
 * }
 */
