/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

/* 移动端适配 */
html {
  font-size: 16px;
}

@media (max-width: 375px) {
  html {
    font-size: 14px;
  }
}

/* 通用按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background: #ff4d6d;
  color: white;
}

.btn-primary:hover {
  background: #e63946;
}

.btn-secondary {
  background: #f0f0f0;
  color: #666;
}

.btn-secondary:hover {
  background: #e0e0e0;
}

/* 通用卡片样式 */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 16px;
  margin-bottom: 16px;
}

/* 通用表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.form-label.required::before {
  content: '*';
  color: #ff4d6d;
  margin-right: 4px;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background: #f8f8f8;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #ff4d6d;
  background: white;
}

/* 通用标签样式 */
.tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
}

.tag-success {
  background: #e8f5e8;
  color: #4caf50;
}

.tag-info {
  background: #e3f2fd;
  color: #2196f3;
}

.tag-warning {
  background: #fff3e0;
  color: #ff9800;
}

/* 通用文本样式 */
.text-primary {
  color: #ff4d6d;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

.text-center {
  text-align: center;
}

.text-small {
  font-size: 12px;
}

/* 通用间距 */
.mb-10 { margin-bottom: 10px; }
.mb-16 { margin-bottom: 16px; }
.mb-20 { margin-bottom: 20px; }
.mt-10 { margin-top: 10px; }
.mt-16 { margin-top: 16px; }
.mt-20 { margin-top: 20px; }

.p-16 { padding: 16px; }
.p-20 { padding: 20px; }

/* 通用布局 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

.gap-8 { gap: 8px; }
.gap-12 { gap: 12px; }
.gap-16 { gap: 16px; }
.gap-20 { gap: 20px; }

/* 通用动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s;
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}
