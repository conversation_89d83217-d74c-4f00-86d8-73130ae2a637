@echo off
echo 正在启动医院预约陪诊应用...
echo.

echo 检查Node.js环境...
node --version
if %errorlevel% neq 0 (
    echo 错误：未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)

echo.
echo 检查npm环境...
npm --version
if %errorlevel% neq 0 (
    echo 错误：未找到npm
    pause
    exit /b 1
)

echo.
echo 安装项目依赖...
npm install
if %errorlevel% neq 0 (
    echo 错误：依赖安装失败
    pause
    exit /b 1
)

echo.
echo 启动开发服务器...
echo 应用将在 http://localhost:8080 打开
echo.
npm run serve

pause
