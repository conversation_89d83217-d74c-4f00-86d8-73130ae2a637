// 医院相关API接口
import request from './config'

/**
 * 医院API接口
 */
export const hospitalApi = {
  /**
   * 获取医院列表
   * @param {Object} params - 查询参数
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @param {string} params.city - 城市
   * @returns {Promise}
   */
  getHospitalList(params = {}) {
    return request({
      url: '/hospitals',
      method: 'get',
      params: {
        page: 1,
        size: 20,
        ...params
      }
    })
  },

  /**
   * 根据ID获取医院详情
   * @param {number} id - 医院ID
   * @returns {Promise}
   */
  getHospitalDetail(id) {
    return request({
      url: `/hospitals/${id}`,
      method: 'get'
    })
  },

  /**
   * 搜索医院
   * @param {string} keyword - 搜索关键词
   * @returns {Promise}
   */
  searchHospitals(keyword) {
    return request({
      url: '/hospitals/search',
      method: 'get',
      params: { keyword }
    })
  },

  /**
   * 获取医院的接待地点列表
   * @param {number} hospitalId - 医院ID
   * @returns {Promise}
   */
  getHospitalLocations(hospitalId) {
    return request({
      url: `/hospitals/${hospitalId}/locations`,
      method: 'get'
    })
  },

  /**
   * 获取医院的可预约时间段
   * @param {number} hospitalId - 医院ID
   * @param {string} date - 日期 (YYYY-MM-DD)
   * @returns {Promise}
   */
  getAvailableTimeSlots(hospitalId, date) {
    return request({
      url: `/hospitals/${hospitalId}/time-slots`,
      method: 'get',
      params: { date }
    })
  }
}

/**
 * 预约相关API接口
 */
export const bookingApi = {
  /**
   * 创建预约
   * @param {Object} bookingData - 预约数据
   * @param {number} bookingData.hospitalId - 医院ID
   * @param {string} bookingData.patientName - 患者姓名
   * @param {string} bookingData.patientPhone - 患者电话
   * @param {string} bookingData.location - 接待地址
   * @param {string} bookingData.serviceTime - 陪诊时间
   * @param {string} bookingData.requirements - 服务需求
   * @returns {Promise}
   */
  createBooking(bookingData) {
    return request({
      url: '/bookings',
      method: 'post',
      data: bookingData
    })
  },

  /**
   * 获取用户的预约列表
   * @param {Object} params - 查询参数
   * @param {string} params.status - 预约状态 (pending/completed/cancelled)
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @returns {Promise}
   */
  getMyBookings(params = {}) {
    return request({
      url: '/bookings/my',
      method: 'get',
      params: {
        page: 1,
        size: 20,
        ...params
      }
    })
  },

  /**
   * 根据ID获取预约详情
   * @param {number} id - 预约ID
   * @returns {Promise}
   */
  getBookingDetail(id) {
    return request({
      url: `/bookings/${id}`,
      method: 'get'
    })
  },

  /**
   * 取消预约
   * @param {number} id - 预约ID
   * @param {string} reason - 取消原因
   * @returns {Promise}
   */
  cancelBooking(id, reason = '') {
    return request({
      url: `/bookings/${id}/cancel`,
      method: 'put',
      data: { reason }
    })
  },

  /**
   * 确认预约完成
   * @param {number} id - 预约ID
   * @returns {Promise}
   */
  completeBooking(id) {
    return request({
      url: `/bookings/${id}/complete`,
      method: 'put'
    })
  },

  /**
   * 对预约进行评价
   * @param {number} id - 预约ID
   * @param {Object} reviewData - 评价数据
   * @param {number} reviewData.rating - 评分 (1-5)
   * @param {string} reviewData.comment - 评价内容
   * @returns {Promise}
   */
  reviewBooking(id, reviewData) {
    return request({
      url: `/bookings/${id}/review`,
      method: 'post',
      data: reviewData
    })
  }
}

/**
 * 用户相关API接口
 */
export const userApi = {
  /**
   * 用户登录
   * @param {Object} loginData - 登录数据
   * @param {string} loginData.phone - 手机号
   * @param {string} loginData.code - 验证码
   * @returns {Promise}
   */
  login(loginData) {
    return request({
      url: '/auth/login',
      method: 'post',
      data: loginData
    })
  },

  /**
   * 发送验证码
   * @param {string} phone - 手机号
   * @returns {Promise}
   */
  sendSmsCode(phone) {
    return request({
      url: '/auth/sms-code',
      method: 'post',
      data: { phone }
    })
  },

  /**
   * 获取用户信息
   * @returns {Promise}
   */
  getUserInfo() {
    return request({
      url: '/user/info',
      method: 'get'
    })
  },

  /**
   * 更新用户信息
   * @param {Object} userInfo - 用户信息
   * @returns {Promise}
   */
  updateUserInfo(userInfo) {
    return request({
      url: '/user/info',
      method: 'put',
      data: userInfo
    })
  },

  /**
   * 用户退出登录
   * @returns {Promise}
   */
  logout() {
    return request({
      url: '/auth/logout',
      method: 'post'
    })
  }
}

/**
 * 系统相关API接口
 */
export const systemApi = {
  /**
   * 获取系统配置
   * @returns {Promise}
   */
  getSystemConfig() {
    return request({
      url: '/system/config',
      method: 'get'
    })
  },

  /**
   * 上传文件
   * @param {File} file - 文件对象
   * @returns {Promise}
   */
  uploadFile(file) {
    const formData = new FormData()
    formData.append('file', file)
    
    return request({
      url: '/system/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 获取客服联系方式
   * @returns {Promise}
   */
  getCustomerService() {
    return request({
      url: '/system/customer-service',
      method: 'get'
    })
  }
}
