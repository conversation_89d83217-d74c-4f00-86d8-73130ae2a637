<template>
  <div class="my-bookings">
    <div class="header">
      <div class="nav-header">
        <button class="back-btn" @click="goBack">‹</button>
        <h1 class="title">我的预约</h1>
      </div>
    </div>
    
    <div class="bookings-content">
      <!-- 预约统计 -->
      <div class="stats-section">
        <div class="stats-card">
          <div class="stat-item">
            <div class="stat-number">{{ bookings.length }}</div>
            <div class="stat-label">总预约</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ pendingCount }}</div>
            <div class="stat-label">待服务</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ completedCount }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
      </div>

      <!-- 预约列表 -->
      <div class="bookings-list">
        <div 
          v-for="booking in bookings" 
          :key="booking.id"
          class="booking-card"
          @click="viewBookingDetail(booking)"
        >
          <div class="booking-header">
            <div class="hospital-info">
              <h3 class="hospital-name">{{ booking.hospital }}</h3>
              <span class="booking-status" :class="booking.status">
                {{ getStatusText(booking.status) }}
              </span>
            </div>
            <div class="booking-time">{{ booking.bookingTime }}</div>
          </div>
          
          <div class="booking-details">
            <div class="detail-row">
              <span class="label">接待地址：</span>
              <span class="value">{{ booking.location }}</span>
            </div>
            <div class="detail-row">
              <span class="label">陪诊时间：</span>
              <span class="value">{{ booking.serviceTime }}</span>
            </div>
            <div class="detail-row">
              <span class="label">联系方式：</span>
              <span class="value">{{ booking.contact }}</span>
            </div>
            <div class="detail-row" v-if="booking.requirements">
              <span class="label">服务需求：</span>
              <span class="value">{{ booking.requirements }}</span>
            </div>
          </div>
          
          <div class="booking-actions">
            <button 
              v-if="booking.status === 'pending'" 
              class="action-btn cancel-btn"
              @click.stop="cancelBooking(booking)"
            >
              取消预约
            </button>
            <button 
              v-if="booking.status === 'completed'" 
              class="action-btn review-btn"
              @click.stop="writeReview(booking)"
            >
              写评价
            </button>
            <button 
              class="action-btn contact-btn"
              @click.stop="contactService(booking)"
            >
              联系客服
            </button>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="bookings.length === 0" class="empty-state">
          <div class="empty-icon">📅</div>
          <div class="empty-text">暂无预约记录</div>
          <div class="empty-desc">快去预约陪诊服务吧</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'MyBookings',
  emits: ['back'],
  setup(props, { emit }) {
    // 模拟预约数据
    const bookings = ref([
      {
        id: 1,
        hospital: '宁波市鄞州人民医院',
        location: '东门口',
        serviceTime: '2024-01-15 09:00-12:00',
        contact: '138****8888',
        requirements: '需要轮椅陪同，老人行动不便',
        status: 'pending',
        bookingTime: '2024-01-10 14:30'
      },
      {
        id: 2,
        hospital: '宁波市第一医院',
        location: '南门口',
        serviceTime: '2024-01-08 14:00-17:00',
        contact: '139****9999',
        requirements: '',
        status: 'completed',
        bookingTime: '2024-01-05 10:20'
      },
      {
        id: 3,
        hospital: '宁波市中医院',
        location: '正门口',
        serviceTime: '2024-01-20 08:30-11:30',
        contact: '137****7777',
        requirements: '陪同做检查，需要女性陪诊员',
        status: 'pending',
        bookingTime: '2024-01-12 16:45'
      }
    ])

    // 计算统计数据
    const pendingCount = computed(() => 
      bookings.value.filter(b => b.status === 'pending').length
    )
    
    const completedCount = computed(() => 
      bookings.value.filter(b => b.status === 'completed').length
    )

    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        'pending': '待服务',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    }

    // 返回上一页
    const goBack = () => {
      emit('back')
    }

    // 查看预约详情
    const viewBookingDetail = (booking) => {
      console.log('查看预约详情:', booking)
      // 这里可以跳转到详情页面或显示详情弹窗
    }

    // 取消预约
    const cancelBooking = (booking) => {
      if (confirm('确定要取消这个预约吗？')) {
        booking.status = 'cancelled'
        console.log('取消预约:', booking)
      }
    }

    // 写评价
    const writeReview = (booking) => {
      console.log('写评价:', booking)
      // 这里可以跳转到评价页面
    }

    // 联系客服
    const contactService = (booking) => {
      console.log('联系客服:', booking)
      // 这里可以打开客服聊天或拨打电话
    }

    return {
      bookings,
      pendingCount,
      completedCount,
      getStatusText,
      goBack,
      viewBookingDetail,
      cancelBooking,
      writeReview,
      contactService
    }
  }
}
</script>

<style scoped>
/* 整体容器 - 全屏覆盖 */
.my-bookings {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f5f6fa;
  z-index: 1000;
  max-width: 375px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', sans-serif;
  overflow-y: auto;
}

/* 头部 */
.header {
  background: white;
  padding-bottom: 20px;
}

.nav-header {
  display: flex;
  align-items: center;
  padding: 0 20px;
  position: relative;
}

.back-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #333;
  cursor: pointer;
  padding: 12px 0;
  margin-right: 12px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #333;
  padding: 12px 0;
}

/* 内容区域 */
.bookings-content {
  padding: 0 20px 40px 20px;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 20px;
}

.stats-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #007aff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

/* 预约列表 */
.bookings-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.booking-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.booking-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.booking-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.hospital-info {
  flex: 1;
}

.hospital-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
}

.booking-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.booking-status.pending {
  background: #fff3cd;
  color: #856404;
}

.booking-status.completed {
  background: #d4edda;
  color: #155724;
}

.booking-status.cancelled {
  background: #f8d7da;
  color: #721c24;
}

.booking-time {
  font-size: 12px;
  color: #999;
}

.booking-details {
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  margin-bottom: 4px;
  font-size: 14px;
}

.label {
  color: #666;
  min-width: 80px;
}

.value {
  color: #333;
  flex: 1;
}

.booking-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: #ff4757;
  color: white;
}

.review-btn {
  background: #ffa502;
  color: white;
}

.contact-btn {
  background: #007aff;
  color: white;
}

.action-btn:hover {
  opacity: 0.8;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #999;
}
</style>
