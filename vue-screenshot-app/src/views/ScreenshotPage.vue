<template>
  <div class="hospital-app">
    <!-- 医院列表页 -->
    <HospitalList
      v-if="currentPage === 'hospitalList'"
      @select-hospital="handleSelectHospital"
      @go-to-my-bookings="goToMyBookings"
    />

    <!-- 预约服务页 -->
    <BookingForm
      v-if="currentPage === 'booking'"
      :hospital="selectedHospital"
      @back="handleBack"
      @show-location-picker="showLocationPicker = true"
      @show-time-picker="showTimePicker = true"
      @submit="handleSubmitBooking"
      ref="bookingFormRef"
    />

    <!-- 接待地点选择页 -->
    <LocationPicker
      v-if="showLocationPicker"
      @back="showLocationPicker = false"
      @select="handleLocationSelect"
    />

    <!-- 陪诊时间选择页 -->
    <TimePicker
      v-if="showTimePicker"
      @back="showTimePicker = false"
      @confirm="handleTimeConfirm"
    />

    <!-- 我的预约页 -->
    <MyBookings
      v-if="currentPage === 'myBookings'"
      @back="handleBack"
    />
  </div>
</template>

<script>
import { ref } from 'vue'
import HospitalList from '@/components/HospitalList.vue'
import BookingForm from '@/components/BookingForm.vue'
import LocationPicker from '@/components/LocationPicker.vue'
import TimePicker from '@/components/TimePicker.vue'
import MyBookings from '@/components/MyBookings.vue'

export default {
  name: 'HospitalApp',
  components: {
    HospitalList,
    BookingForm,
    LocationPicker,
    TimePicker,
    MyBookings
  },
  setup() {
    // 页面状态
    const currentPage = ref('hospitalList')
    const showLocationPicker = ref(false)
    const showTimePicker = ref(false)
    const selectedHospital = ref(null)
    const bookingFormRef = ref(null)

    // 方法
    const handleSelectHospital = (hospital) => {
      selectedHospital.value = hospital
      currentPage.value = 'booking'
    }

    const handleBack = () => {
      currentPage.value = 'hospitalList'
      selectedHospital.value = null
    }

    const handleLocationSelect = (location) => {
      showLocationPicker.value = false
      if (bookingFormRef.value) {
        bookingFormRef.value.updateLocation(location)
      }
    }

    const handleTimeConfirm = (time) => {
      showTimePicker.value = false
      if (bookingFormRef.value) {
        bookingFormRef.value.updateTime(time)
      }
    }

    const handleSubmitBooking = (formData) => {
      console.log('提交预约:', formData)
      // 这里可以添加提交逻辑
      alert('预约提交成功！')
    }

    // 跳转到我的预约
    const goToMyBookings = () => {
      currentPage.value = 'myBookings'
    }

    return {
      currentPage,
      showLocationPicker,
      showTimePicker,
      selectedHospital,
      bookingFormRef,
      handleSelectHospital,
      handleBack,
      handleLocationSelect,
      handleTimeConfirm,
      handleSubmitBooking,
      goToMyBookings
    }
  }
}
</script>

<style scoped>
/* 基础样式 - 微信公众号H5风格 */
.hospital-app {
  max-width: 100%;
  margin: 0;
  background: #f7f8fa;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei', Arial, sans-serif;
  position: relative;
  /* 防止页面缩放 */
  touch-action: manipulation;
  -webkit-text-size-adjust: 100%;
}

/* 微信公众号适配 */
@media (max-width: 414px) {
  .hospital-app {
    font-size: 14px;
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .hospital-app {
    max-width: 100%;
  }
}
</style>
