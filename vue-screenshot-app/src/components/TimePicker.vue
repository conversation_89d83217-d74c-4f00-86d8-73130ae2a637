<template>
  <div class="time-picker">
    <div class="header">
      <div class="nav-header">
        <button class="back-btn" @click="goBack">‹</button>
        <h1 class="title">陪诊时间</h1>
      </div>
    </div>
    
    <div class="time-content">
      <div class="time-header">
        <span class="time-icon">🕐</span>
        <span class="start-time">开始时间</span>
        <span class="to">至</span>
        <span class="end-time">结束时间</span>
      </div>
      
      <div class="time-picker-section">
        <div class="time-column">
          <div class="time-label">开始时间</div>
          <div class="picker-wheel">
            <div class="picker-item">▲</div>
            <div class="picker-item">▲</div>
            <div class="picker-item">▲</div>
            <div 
              v-for="hour in hours" 
              :key="`start-${hour}`"
              :class="['picker-value', { active: selectedStartHour === hour }]"
              @click="selectStartHour(hour)"
            >
              {{ hour }}
            </div>
            <div class="picker-item">▼</div>
            <div class="picker-item">▼</div>
            <div class="picker-item">▼</div>
          </div>
        </div>
        
        <div class="time-column">
          <div class="time-label">结束时间</div>
          <div class="picker-wheel">
            <div class="picker-item">▲</div>
            <div class="picker-item">▲</div>
            <div class="picker-item">▲</div>
            <div 
              v-for="hour in hours" 
              :key="`end-${hour}`"
              :class="['picker-value', { active: selectedEndHour === hour }]"
              @click="selectEndHour(hour)"
            >
              {{ hour }}
            </div>
            <div class="picker-item">▼</div>
            <div class="picker-item">▼</div>
            <div class="picker-item">▼</div>
          </div>
        </div>
      </div>
      
      <div class="time-actions">
        <button class="cancel-btn" @click="goBack">取消</button>
        <button class="confirm-btn" @click="confirmTime">确定</button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'TimePicker',
  emits: ['back', 'confirm'],
  setup(props, { emit }) {
    // 时间数据
    const selectedStartHour = ref('08')
    const selectedEndHour = ref('10')
    
    // 生成小时选项
    const hours = computed(() => {
      const hourList = []
      for (let i = 7; i <= 18; i++) {
        hourList.push(i.toString().padStart(2, '0'))
      }
      return hourList
    })
    
    // 方法
    const goBack = () => {
      emit('back')
    }
    
    const selectStartHour = (hour) => {
      selectedStartHour.value = hour
      // 确保结束时间不早于开始时间
      if (parseInt(selectedEndHour.value) <= parseInt(hour)) {
        selectedEndHour.value = (parseInt(hour) + 2).toString().padStart(2, '0')
      }
    }
    
    const selectEndHour = (hour) => {
      // 确保结束时间不早于开始时间
      if (parseInt(hour) > parseInt(selectedStartHour.value)) {
        selectedEndHour.value = hour
      }
    }
    
    const confirmTime = () => {
      const timeRange = `${selectedStartHour.value}:00 - ${selectedEndHour.value}:00`
      emit('confirm', timeRange)
    }
    
    return {
      selectedStartHour,
      selectedEndHour,
      hours,
      goBack,
      selectStartHour,
      selectEndHour,
      confirmTime
    }
  }
}
</script>

<style scoped>
/* 整体容器 - 全屏覆盖 */
.time-picker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f5f6fa;
  z-index: 1000;
  max-width: 375px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', sans-serif;
}

/* 头部 */
.header {
  background: white;
  padding-bottom: 20px;
}

.nav-header {
  display: flex;
  align-items: center;
  padding: 0 20px;
  position: relative;
}

.back-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #333;
  cursor: pointer;
  position: absolute;
  left: 20px;
}

.title {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  margin: 20px 0;
  color: #333;
  width: 100%;
}

/* 时间选择页面 */
.time-content {
  padding: 20px;
}

.time-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 20px;
  font-size: 14px;
  color: #666;
}

.time-picker-section {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin: 40px 0;
}

.time-column {
  text-align: center;
}

.time-label {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

.picker-wheel {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: #f8f8f8;
  border-radius: 12px;
  padding: 20px 16px;
  min-width: 80px;
  max-height: 300px;
  overflow-y: auto;
}

.picker-item {
  color: #ccc;
  font-size: 12px;
}

.picker-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 40px;
}

.picker-value:hover {
  background: #e3f2fd;
  color: #2196f3;
}

.picker-value.active {
  background: #ff4d6d;
  color: white;
}

.time-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 40px;
}

.cancel-btn,
.confirm-btn {
  padding: 12px 32px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.cancel-btn {
  background: #f0f0f0;
  color: #666;
}

.cancel-btn:hover {
  background: #e0e0e0;
}

.confirm-btn {
  background: #ff4d6d;
  color: white;
}

.confirm-btn:hover {
  background: #e63946;
}
</style>
