<template>
  <div class="booking-form">
    <div class="header">
      <div class="nav-header">
        <button class="back-btn" @click="goBack">‹</button>
        <h1 class="title">预约服务</h1>
      </div>
    </div>
    
    <div class="booking-content">
      <div class="hospital-info-card">
        <div class="hospital-image">
          <div :class="['hospital-img-placeholder', `hospital-${hospital.id}`]">
            <div class="hospital-icon">🏥</div>
          </div>
        </div>
        <div class="hospital-details">
          <div class="hospital-name">{{ hospital.name }}</div>
          <div class="hospital-tags">
            <span 
              v-for="tag in hospital.tags" 
              :key="tag.type"
              :class="['tag', tag.type]"
            >
              {{ tag.text }}
            </span>
          </div>
          <div class="hospital-intro">医院简介：{{ hospital.address }}</div>
        </div>
        <div class="distance">距您：{{ hospital.distance }}</div>
      </div>
      
      <div class="form-section">
        <div class="form-item required">
          <label>服务类型</label>
          <select v-model="form.serviceType">
            <option value="半天">半天</option>
            <option value="全天">全天</option>
          </select>
        </div>
        
        <div class="form-item required">
          <label>您的姓名</label>
          <input 
            type="text" 
            v-model="form.name" 
            placeholder="请写联系人姓名" 
          />
        </div>
        
        <div class="form-item required">
          <label>您的电话</label>
          <input 
            type="tel" 
            v-model="form.phone" 
            placeholder="请写联系人电话" 
          />
        </div>
        
        <div class="form-item required" @click="showLocationPicker">
          <label>接待地址</label>
          <div class="select-field">
            <span>{{ form.location || '请选择' }}</span>
            <span class="arrow">›</span>
          </div>
        </div>

        <div class="form-item required" @click="showTimePicker">
          <label>陪诊时间</label>
          <div class="select-field">
            <span>{{ form.time || '请选择' }}</span>
            <span class="arrow">›</span>
          </div>
        </div>
        
        <div class="form-item">
          <label>服务需求</label>
          <textarea 
            v-model="form.requirements" 
            placeholder="请详细描述，如对陪诊有特别需求，年龄要求等等，没有请留空"
          ></textarea>
        </div>
      </div>
      
      <div class="notice-section">
        <div class="notice-header">
          <span class="notice-icon">⚠️</span>
          <span>注意事项</span>
        </div>
        <div class="notice-content">
          此项服务为陪诊服务，不包含其他业务服务，如有其他业务服务或疑问，请联系客服。
        </div>
        
        <div class="agreement-section">
          <label class="checkbox-label">
            <input type="checkbox" v-model="form.agreed" />
            <span class="checkmark"></span>
            我已阅读并同意《用户协议》和《服务协议》
          </label>
        </div>
      </div>
      
      <button 
        class="book-btn" 
        :disabled="!canSubmit"
        @click="submitBooking"
      >
        立即预约
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'BookingForm',
  props: {
    hospital: {
      type: Object,
      required: true
    }
  },
  emits: ['back', 'show-location-picker', 'show-time-picker', 'submit'],
  setup(props, { emit }) {
    // 表单数据
    const form = ref({
      serviceType: '半天',
      name: '',
      phone: '',
      location: '',
      time: '',
      requirements: '',
      agreed: false
    })
    
    // 计算属性
    const canSubmit = computed(() => {
      return form.value.name && 
             form.value.phone && 
             form.value.location && 
             form.value.time && 
             form.value.agreed
    })
    
    // 方法
    const goBack = () => {
      emit('back')
    }
    
    const showLocationPicker = () => {
      emit('show-location-picker')
    }
    
    const showTimePicker = () => {
      emit('show-time-picker')
    }
    
    const submitBooking = () => {
      if (canSubmit.value) {
        emit('submit', form.value)
      }
    }
    
    // 暴露方法供父组件调用
    const updateLocation = (location) => {
      form.value.location = location
    }
    
    const updateTime = (time) => {
      form.value.time = time
    }
    
    return {
      form,
      canSubmit,
      goBack,
      showLocationPicker,
      showTimePicker,
      submitBooking,
      updateLocation,
      updateTime
    }
  }
}
</script>

<style scoped>
/* 整体容器 - 全屏覆盖 */
.booking-form {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f5f6fa;
  z-index: 1000;
  max-width: 375px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', sans-serif;
  overflow-y: auto;
}

/* 头部样式 - uni-ui导航栏风格 */

.header {
  background: #fff;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-header {
  display: flex;
  align-items: center;
  padding: 44px 15px 12px 15px;
  position: relative;
  min-height: 44px;
}

.back-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #409eff;
  cursor: pointer;
  position: absolute;
  left: 15px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  margin: 0;
  color: #303133;
  width: 100%;
}

/* 预约页面样式 */
.booking-content {
  padding: 20px;
}

.hospital-info-card {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.hospital-img-placeholder {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.hospital-1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hospital-2 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.hospital-3 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.hospital-icon {
  font-size: 24px;
  color: white;
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.hospital-details {
  flex: 1;
}

.hospital-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.hospital-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.tag.tertiary {
  background: #e8f5e8;
  color: #4caf50;
}

.tag.comprehensive {
  background: #e3f2fd;
  color: #2196f3;
}

.hospital-intro {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
}

.distance {
  font-size: 12px;
  color: #999;
  align-self: flex-start;
}

/* 表单样式 */
.form-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

.form-item {
  margin-bottom: 20px;
}

.form-item.required label::before {
  content: '*';
  color: #ff4d6d;
  margin-right: 4px;
}

.form-item label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.form-item input,
.form-item select,
.form-item textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background: #f8f8f8;
  box-sizing: border-box;
}

.form-item textarea {
  height: 80px;
  resize: vertical;
}

.select-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f8f8f8;
  cursor: pointer;
}

.arrow {
  color: #999;
  font-size: 16px;
}

/* 注意事项 */
.notice-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

.notice-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #ff4d6d;
  margin-bottom: 12px;
}

.notice-content {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20px;
}

.agreement-section {
  margin-top: 20px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* 按钮样式 */
.book-btn {
  width: 100%;
  padding: 16px;
  background: #ff4d6d;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s;
}

.book-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.book-btn:not(:disabled):hover {
  background: #e63946;
}
</style>
