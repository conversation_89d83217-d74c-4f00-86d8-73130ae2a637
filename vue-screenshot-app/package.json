{"name": "vue-screenshot-app", "version": "1.0.0", "description": "Vue.js screenshot functionality app", "main": "index.js", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.10.0", "element-plus": "^2.3.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "vue": "^3.3.0", "vue-router": "^4.2.0"}, "devDependencies": {"@vue/cli-service": "^5.0.0", "@vue/compiler-sfc": "^3.3.0"}, "author": "", "license": "MIT"}