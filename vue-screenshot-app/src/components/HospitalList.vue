<template>
  <div class="hospital-list">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-container">
        <div class="search-input-wrapper">
          <span class="search-icon">🔍</span>
          <input
            type="text"
            placeholder="请输入医院名称"
            v-model="searchKeyword"
            @input="onSearch"
            class="search-input"
            autocomplete="off"
            autocorrect="off"
            autocapitalize="off"
            spellcheck="false"
          />
        </div>
        <button class="search-btn" @click="handleSearch">搜索</button>
      </div>
    </div>

    <!-- 导航标签 -->
    <div class="nav-section">
      <div class="service-hospital active">
        <span class="heart">❤️</span>
        <span>服务医院</span>
      </div>
      <div class="my-appointment" @click="goToMyBookings">我的预约</div>
    </div>
    
    <div class="hospital-list-content">
      <div
        v-for="hospital in filteredHospitals"
        :key="hospital.id"
        class="hospital-card"
        @click="selectHospital(hospital)"
      >
        <div class="hospital-card-content">
          <div class="hospital-left">
            <div class="hospital-image">
              <div :class="['hospital-img-placeholder', `hospital-${hospital.id}`]">
                <div class="hospital-icon">🏥</div>
              </div>
            </div>
          </div>
          <div class="hospital-right">
            <div class="hospital-info-top">
              <h3 class="hospital-name">{{ hospital.name }}</h3>
              <div class="distance-info">
                <span class="distance-text">距您：{{ hospital.distance }}</span>
              </div>
            </div>
            <div class="hospital-tags">
              <span
                v-for="tag in hospital.tags"
                :key="tag.type"
                :class="['hospital-tag', tag.type]"
              >
                {{ tag.text }}
              </span>
            </div>
            <div class="hospital-address">
              <span class="address-label">地址：</span>
              <span class="address-text">{{ hospital.address }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部指示器 -->
    <div class="bottom-indicator">
      <div class="indicator-bar"></div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'HospitalList',
  emits: ['select-hospital', 'go-to-my-bookings'],
  setup(props, { emit }) {
    const searchKeyword = ref('')
    
    // 医院数据
    const hospitals = ref([
      {
        id: 1,
        name: '宁波市鄞州人民医院',
        tags: [
          { type: 'tertiary', text: '三甲' },
          { type: 'comprehensive', text: '综合医院' }
        ],
        address: '宁波市鄞州区百丈东路251号',
        distance: '1.6km'
      },
      {
        id: 2,
        name: '宁波市眼科医院',
        tags: [
          { type: 'tertiary', text: '三甲' },
          { type: 'comprehensive', text: '综合医院' }
        ],
        address: '宁波市鄞州区北明程路599号',
        distance: '6km'
      },
      {
        id: 3,
        name: '宁波市第四医院',
        tags: [
          { type: 'tertiary', text: '三甲' },
          { type: 'comprehensive', text: '综合医院' }
        ],
        address: '浙江省宁波市象山月城东合路291号',
        distance: '1.6km'
      }
    ])
    
    // 过滤医院列表
    const filteredHospitals = computed(() => {
      if (!searchKeyword.value) {
        return hospitals.value
      }
      return hospitals.value.filter(hospital => 
        hospital.name.includes(searchKeyword.value) ||
        hospital.address.includes(searchKeyword.value)
      )
    })
    
    // 方法
    const onSearch = () => {
      // 实时搜索逻辑
    }
    
    const handleSearch = () => {
      // 搜索按钮点击逻辑
      console.log('搜索:', searchKeyword.value)
    }
    
    const selectHospital = (hospital) => {
      emit('select-hospital', hospital)
    }

    const goToMyBookings = () => {
      emit('go-to-my-bookings')
    }

    return {
      searchKeyword,
      hospitals,
      filteredHospitals,
      onSearch,
      handleSearch,
      selectHospital,
      goToMyBookings
    }
  }
}
</script>

<style scoped>
/* 整体容器 */
.hospital-list {
  background: #f5f6fa;
  min-height: 100vh;
  max-width: 375px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', sans-serif;
}

/* 搜索区域 - 参考图风格 */
.search-section {
  padding: 20px 20px 16px 20px;
  background: white;
}

.search-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  z-index: 1;
  color: #999;
  font-size: 16px;
}

.search-input {
  width: 100%;
  padding: 12px 12px 12px 36px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  background: #f8f9fa;
  color: #333;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #007aff;
  background: white;
}

.search-input::placeholder {
  color: #999;
}

.search-btn {
  padding: 12px 20px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  min-width: 60px;
  transition: all 0.2s ease;
}

.search-btn:hover {
  background: #0056cc;
}

.search-btn:active {
  background: #004499;
}

/* 导航区域 - 参考图风格 */
.nav-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.service-hospital {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ff4757;
  font-weight: 600;
  font-size: 15px;
  padding: 8px 0;
  position: relative;
}

.service-hospital.active::after {
  content: '';
  position: absolute;
  bottom: -16px;
  left: 0;
  right: 0;
  height: 2px;
  background: #ff4757;
  border-radius: 1px;
}

.service-hospital .heart {
  font-size: 16px;
}

.my-appointment {
  color: #007aff;
  font-weight: 500;
  font-size: 15px;
  cursor: pointer;
  padding: 8px 0;
  transition: color 0.2s;
}

.my-appointment:hover {
  color: #0056cc;
}

/* 医院列表 - 参考图风格 */
.hospital-list-content {
  padding: 0 20px 40px 20px;
  background: #f5f6fa;
}

.hospital-card {
  background: white;
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.hospital-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.hospital-card:active {
  transform: translateY(0);
}

.hospital-card-content {
  padding: 16px;
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.hospital-left {
  flex-shrink: 0;
}

.hospital-image {
  width: 60px;
  height: 60px;
}

.hospital-img-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.hospital-1 {
  background: #007aff;
}

.hospital-2 {
  background: #34c759;
}

.hospital-3 {
  background: #ff9500;
}

.hospital-icon {
  font-size: 24px;
  color: white;
}

.hospital-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.hospital-info-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
}

.hospital-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.4;
  flex: 1;
}

.distance-info {
  margin-left: 8px;
  flex-shrink: 0;
}

.distance-text {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
}

.hospital-tags {
  display: flex;
  gap: 6px;
  margin-bottom: 6px;
}

.hospital-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  line-height: 1.2;
  border: 1px solid;
}

.hospital-tag.tertiary {
  background: #fff2f0;
  color: #ff4757;
  border-color: #ff4757;
}

.hospital-tag.comprehensive {
  background: #f0f9ff;
  color: #007aff;
  border-color: #007aff;
}

.hospital-address {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.address-label {
  color: #999;
}

.address-text {
  color: #666;
}

/* 底部指示器 */
.bottom-indicator {
  padding: 20px;
  display: flex;
  justify-content: center;
  background: #f5f6fa;
}

.indicator-bar {
  width: 134px;
  height: 5px;
  background: #000;
  border-radius: 3px;
}
</style>
