<template>
  <div class="location-picker">
    <div class="header">
      <div class="nav-header">
        <button class="back-btn" @click="goBack">‹</button>
        <h1 class="title">接待地点</h1>
      </div>
    </div>
    
    <div class="location-content">
      <div 
        v-for="location in locations" 
        :key="location.id"
        class="location-item"
      >
        <div class="location-info">
          <div class="location-name">{{ location.name }}</div>
          <div class="location-address">地址：{{ location.address }}</div>
        </div>
        <div class="location-status" :class="{ selected: location.isDefault }">
          <span :class="location.isDefault ? 'check-icon' : 'radio-icon'">
            {{ location.isDefault ? '✓' : '○' }}
          </span>
          <span>{{ location.isDefault ? '已设为默认地址' : '设为默认地址' }}</span>
        </div>
        <button 
          :class="['select-btn', { selected: location.isDefault }]"
          @click="selectLocation(location)"
        >
          选择
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'LocationPicker',
  emits: ['back', 'select'],
  setup(props, { emit }) {
    // 地点数据
    const locations = ref([
      {
        id: 1,
        name: '东门口',
        address: '宁波市鄞州区百丈东路251号',
        isDefault: true
      },
      {
        id: 2,
        name: '南侧门',
        address: '宁波市鄞州区百丈东路277号',
        isDefault: false
      },
      {
        id: 3,
        name: '正门口',
        address: '宁波市鄞州区百丈东路288号',
        isDefault: false
      }
    ])
    
    // 方法
    const goBack = () => {
      emit('back')
    }
    
    const selectLocation = (location) => {
      // 更新默认地址
      locations.value.forEach(loc => {
        loc.isDefault = loc.id === location.id
      })
      
      // 发送选择事件
      emit('select', location.name)
    }
    
    return {
      locations,
      goBack,
      selectLocation
    }
  }
}
</script>

<style scoped>
/* 整体容器 - 全屏覆盖 */
.location-picker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f5f6fa;
  z-index: 1000;
  max-width: 375px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', sans-serif;
}

/* 头部 */
.header {
  background: white;
  padding-bottom: 20px;
}

.nav-header {
  display: flex;
  align-items: center;
  padding: 0 20px;
  position: relative;
}

.back-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #333;
  cursor: pointer;
  position: absolute;
  left: 20px;
}

.title {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  margin: 20px 0;
  color: #333;
  width: 100%;
}

/* 地点选择页面 */
.location-content {
  padding: 20px;
}

.location-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.location-info {
  flex: 1;
}

.location-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.location-address {
  font-size: 12px;
  color: #666;
}

.location-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.location-status.selected {
  color: #ff4d6d;
}

.check-icon {
  color: #ff4d6d;
}

.radio-icon {
  color: #ccc;
}

.select-btn {
  padding: 8px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  background: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.select-btn:hover {
  border-color: #ff4d6d;
  color: #ff4d6d;
}

.select-btn.selected {
  background: #ff4d6d;
  color: white;
  border-color: #ff4d6d;
}
</style>
