# 医院预约陪诊应用

这是一个基于Vue 3的移动端医院预约陪诊应用，包含以下功能页面：

## 功能页面

1. **医院列表页** - 显示可预约的医院列表
2. **预约服务页** - 填写预约信息表单
3. **接待地点选择页** - 选择医院接待地点
4. **陪诊时间选择页** - 选择陪诊的开始和结束时间

## 技术栈

- Vue 3 (Composition API)
- Vue Router 4
- Element Plus (UI组件库)
- 响应式设计 (适配手机端)

## 安装和运行

### 安装依赖
```bash
cd vue-screenshot-app
npm install
```

### 开发环境运行
```bash
npm run serve
```

### 构建生产版本
```bash
npm run build
```

## 项目结构

```
vue-screenshot-app/
├── public/
│   └── index.html
├── src/
│   ├── views/
│   │   └── ScreenshotPage.vue  # 主要页面组件
│   ├── router/
│   │   └── index.js           # 路由配置
│   ├── App.vue               # 根组件
│   └── main.js              # 入口文件
├── package.json
├── vue.config.js
└── README.md
```

## 页面功能

### 医院列表页
- 搜索医院功能
- 显示医院信息（名称、标签、地址、距离）
- 点击医院进入预约页面

### 预约服务页
- 医院信息展示
- 服务类型选择（半天/全天）
- 个人信息填写（姓名、电话）
- 接待地址选择
- 陪诊时间选择
- 服务需求描述
- 用户协议确认

### 接待地点选择页
- 显示医院的多个接待地点
- 支持设置默认地址
- 地点选择功能

### 陪诊时间选择页
- 开始时间和结束时间选择
- 滚轮式时间选择器
- 时间确认功能

## 移动端适配

- 响应式设计，适配手机屏幕
- 触摸友好的交互设计
- 模拟手机状态栏显示
- 优化的移动端用户体验

## 组件架构

项目采用组件化开发，主要组件包括：

- **HospitalList.vue** - 医院列表组件
- **BookingForm.vue** - 预约表单组件
- **LocationPicker.vue** - 地点选择组件
- **TimePicker.vue** - 时间选择组件
- **StatusBar.vue** - 状态栏组件

## 快速启动

### Windows用户
```bash
# 双击运行
start.bat
```

### Linux/Mac用户
```bash
# 给脚本执行权限
chmod +x start.sh
# 运行脚本
./start.sh
```

### 手动启动
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run serve
```

## 特性

- ✅ 组件化架构，易于维护
- ✅ 响应式设计，适配移动端
- ✅ Vue 3 Composition API
- ✅ 模拟真实手机界面
- ✅ 完整的预约流程
- ✅ 表单验证
- ✅ 状态管理

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

建议使用Chrome浏览器的移动设备模拟器查看效果。
