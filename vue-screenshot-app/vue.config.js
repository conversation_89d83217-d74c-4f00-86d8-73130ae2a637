const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  devServer: {
    port: 8080,
    host: '0.0.0.0',  // 允许外部IP访问
    open: true,
    allowedHosts: 'all', // 允许所有主机访问
    client: {
      webSocketURL: 'auto://0.0.0.0:0/ws'
    }
  },
  css: {
    loaderOptions: {
      scss: {
        additionalData: `@import "~@/styles/variables.scss";`
      }
    }
  }
})
