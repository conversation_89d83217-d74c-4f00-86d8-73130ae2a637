package com.ruoyi.ninan.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.utils.FaceEngineTest;
import com.ruoyi.common.core.utils.file.FileTuMultipartFileUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.ninan.domain.*;
import com.ruoyi.ninan.mapper.UserInfoMapper;
import com.ruoyi.ninan.service.*;
import com.ruoyi.ninan.utils.log.NinanLog;
import com.ruoyi.ninan.utils.oss.ALiOssUploadUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;

/**
 * 订单Controller
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@RestController
@RequestMapping("/dd")
public class DdController extends BaseController {
    @Autowired
    private IDdService ddService;

    @Autowired
    private IDdXcqkService ddXcqkService;

    @Autowired
    private IDdCymdService ddCymdService;

    @Autowired
    private IDdSfmxService ddSfmxService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;


    @Autowired
    private UserInfoMapper userInfoService;

    @Autowired
    private IDdQdService ddQdService;

    //订单分为 订单表  服务周期表  定金尾款记录表   人员接单表（包括考勤， 每次结束时间是否需要加入工资 还是统一在人员表）   订单设备表  设备表   订单现场情况表
    //接单往参与名单里面添加
    //接单只能接全部（各种限制 ，接满了是否还要让进去查看 等等）


    /**
     * 找人订单列表
     * rwmc 任务名称
     * ddzt 订单状态
     *
     * @return
     */
    @GetMapping("/getListZr")
    public TableDataInfo getListZr(Dd dd) {
        startPage();
        return getDataTable(ddService.selectDdList(dd));
    }

    //统一实名认证修改
    @GetMapping("/test")
    public AjaxResult test(String url,Long userId) {
        File tpsb = FaceEngineTest.tpsb(url);
        if (tpsb != null) {
            UserInfo userInfo = new UserInfo();
            System.out.println("进入成功---------------");
            MultipartFile multipartFile = FileTuMultipartFileUtils.getMultipartFile(tpsb);
            userInfo.setTxz(ALiOssUploadUtils.uploadFile(multipartFile));
            userInfo.setId(userId);
            userInfoService.updateUserInfo(userInfo);
            tpsb.delete();

            String savePath = "/home/<USER>";
            new File(savePath).delete();
        }
//        List<UserInfo> dds = userInfoService.selectUserInfoList(new UserInfo());
//        if (dds.size() > 0) {
//            for (UserInfo dd : dds) {
//                try {
//                    if (StringUtils.isNotEmpty(dd.getXxz())) {
//                        System.out.println("执行-------------" + url);
//                        File tpsb = FaceEngineTest.tpsb(dd.getXxz());
//                        if (tpsb != null) {
//                            System.out.println("进入成功---------------");
//                            MultipartFile multipartFile = FileTuMultipartFileUtils.getMultipartFile(tpsb);
//                            dd.setTxz(ALiOssUploadUtils.uploadFile(multipartFile));
//                            userInfoService.updateUserInfo(dd);
//                            tpsb.delete();
//                        }
//                    }
//                } catch (Exception e) {
//
//                }finally {
//                    String savePath = "/home/<USER>";
//                    new File(savePath).delete();
//                }
//            }
//        }
        return success();
    }

    /**
     * 临时签到人员导出
     * @param ddQd
     */
    @GetMapping("/exports")
    public AjaxResult exports(DdQd ddQd) throws IOException {
        List<DdQd> list = ddQdService.selectDdQdList(ddQd);
        File file = FileTuMultipartFileUtils.exportToExcel(list, DdQd.class);
        MultipartFile multipartFile = FileTuMultipartFileUtils.getMultipartFile(file);
        return success("签到成功",ALiOssUploadUtils.uploadFile(multipartFile));
    }




    /**
     * 签到人员列表查询
     * @param ddQd
     * @return
     */
    @GetMapping("/qdryList")
    public TableDataInfo qdryList(DdQd ddQd)
    {
        startPage();
        List<DdQd> list = ddQdService.selectDdQdList(ddQd);
        return getDataTable(list);
    }


    /**
     * 新增订单临时人员
     */
    @PostMapping("/qdryInsert")
    public AjaxResult qdryInsert(@RequestBody DdQd ddQd)
    {
        return toAjax(ddQdService.insertDdQd(ddQd));
    }


    /**
     * 订单列表
     *
     * @param dd
     * @return
     */
    @GetMapping("/getListDd")
    public TableDataInfo getListDd(Dd dd) {
        startPage();
        return getDataTable(ddService.selectDdListLb(dd));
    }


    /**
     * 订单最新发布
     */
    @GetMapping("/getListDdZxfb")
    public TableDataInfo getListDdZxfb() {
        startPage();
        return getDataTable(ddService.getListDdZxfb());
    }


    /**
     * 获取设备列表
     */
    @GetMapping("/getListSblb")
    public AjaxResult getListSblb() {
        return success(ddService.getListSblb());
    }


    /**
     * 获取订单详细信息 订单详情接口要加状态了
     *
     * @param id   订单主键id
     * @param dqzt 订单当前状态 三种状态 1 发单的  2 接单的  3 队长接单的
     * @return
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo(Long id, Long dqzt) {
        return success(ddService.selectDdById(id, dqzt));
    }


    /**
     * 查询订单各种状态统计
     *
     * @param tjzt 1：找活统计，2：发单统计，3：找活订单统计
     * @return
     */
    @GetMapping("/countOrders")
    public AjaxResult countOrders(Long tjzt) {
        return success(ddService.countOrders(tjzt));
    }

    /**
     * 查询订单名称备注是否加急
     *
     * @param ddid
     * @return
     */
    @GetMapping("/getInfoXq")
    public AjaxResult getInfoXq(Long ddid) {
        return success(ddService.selectDdByIdKt(ddid));
    }

    /**
     * 删除订单（假删除）
     */
    @NinanLog(title = "删除订单", businessType = BusinessType.DELETE)
    @GetMapping("/deleteDd")
    public AjaxResult deleteDd(Long id) {
        return success(ddService.deleteDdById(id));
    }


    /**
     * 发单人修改订单状态(取消订单)
     */
    @NinanLog(title = "修改订单状态", businessType = BusinessType.UPDATE)
    @GetMapping("/updateDdzt")
    public AjaxResult updateDdzt(Dd dd) {

        //取消订单需要返回定金 并且扣除一定手续费  退款成功还需要提示用户以退款
//        WxAppPayUtils.weChatBank();
        return success(ddService.updateDdzt(dd));
    }

    /**
     * 保安员取消订单
     * @param ddid
     * @return
     */
    @GetMapping("/BaQxDd")
    public AjaxResult BaQxDd(Long ddid) {
        return ddCymdService.BaDzQxDd(ddid);
    }


    /**
     * 保安员打卡
     * @param ddbh 订单编号
     * @param dkdz 打卡地址
     * @param dkjd 打卡经度
     * @param dkwd 打卡纬度
     * @return
     */
    @GetMapping("/BaSmDk")
    public AjaxResult BaSmDk(String ddbh,String dkdz,String dkjd,String dkwd){
        return ddCymdService.BaSmDk(ddbh,dkdz,dkjd,dkwd);
    }


    /**
     * 队长情况总结
     */
    @NinanLog(title = "队长情况总结", businessType = BusinessType.UPDATE)
    @GetMapping("/updateDzQkZj")
    public AjaxResult updateDzQkZj(Dd dd) {
        dd.setDdzt(6L);//改成待结算
        return success(ddService.updateDdQxdd(dd));
    }


    /**
     * 内部带支付变招募中
     * @param dd
     * @return
     */
    @NinanLog(title = "内部带支付变招募中", businessType = BusinessType.UPDATE)
    @GetMapping("/updateNbZfZm")
    public AjaxResult updateNbZfZm(Dd dd) {
        dd.setDdzt(4L);//改成招募中
        return success(ddService.updateDdQxdd(dd));
    }


    /**
     * 队长情况总结查看
     */
    @GetMapping("/getDzqkZj")
    public AjaxResult getDzqkZj(Long ddid) {
        return success(ddService.getDzqkZj(ddid));
    }


    /**
     * 平台招募发布
     * @param ddid 订单主键id
     * @return
     */
    @GetMapping("/ptZmFbP")
    public AjaxResult ptZmFbP(Long ddid,Long nan,Long nv) {
        return success(ddService.ptZmFbP(ddid,nan,nv));
    }


    /**
     * 新增订单
     */
    @NinanLog(title = "订单新增", businessType = BusinessType.INSERT)
    @PostMapping("/insert")
    public AjaxResult insert(@RequestBody Dd dd) {
        return toAjax(ddService.insertDd(dd));
    }


    /**
     * 现场情况上传
     *
     * @param ddXcqk
     * @return
     */
    @NinanLog(title = "现场情况上传", businessType = BusinessType.INSERT)
    @PostMapping("/insertXcqk")
    public AjaxResult insertXcqk(@RequestBody DdXcqk ddXcqk) {
        return success(ddXcqkService.insertDdXcqk(ddXcqk));
    }


    /**
     * 现场情况查询
     *
     * @param ddid
     * @return
     */
    @GetMapping("/getListXcqk")
    public AjaxResult getListXcqk(Long ddid) {
        DdXcqk ddXcqk = new DdXcqk();
        ddXcqk.setDdid(ddid);
        return success(ddXcqkService.selectDdXcqkList(ddXcqk));
    }

    /**
     * 现场情况查询
     *
     * @param id 现场情况的id
     * @return
     */
    @GetMapping("/getXcqkXq")
    public AjaxResult getXcqkXq(Long id) {
        return success(ddXcqkService.selectDdXcqkById(id));
    }


    /**
     * 考勤明细查询
     *
     * @param ddid 订单id
     * @return
     */
    @GetMapping("/getListKqmx")
    public AjaxResult getListKqmx(Long ddid) {
        return success(ddCymdService.selectDdCymdListKqmx(ddid));
    }

    /**
     * 打卡明细
     *
     * @param fwzqId 服务周期id
     * @return
     */
    @GetMapping("/getListDkmx")
    public AjaxResult getListDkmx(Long fwzqId) {
        return success(ddCymdService.selectDdCymdListDkmx(fwzqId));
    }

    /**
     * 人员清单
     *
     * @param ddid
     * @return
     */
    @GetMapping("/getListRyqd")
    public AjaxResult getListRyqd(Long ddid) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("jdZrs", ddCymdService.getJdZrs(ddid));
        jsonObject.put("ryqdList", ddCymdService.selectDdCymdListRyqd(ddid));
        return success(jsonObject);
    }


    /**
     * 用户接单
     */


    /**
     * 取消接单
     */


    /**
     * 订单详情接口要加状态了 三种状态 发单的  接单的  队长接单的
     */


    /**
     * 扫码打卡
     */


    /**
     * 查询订单列表
     */
//    @RequiresPermissions("ninan:dd:list")
    @GetMapping("/list")
    public TableDataInfo list(Dd dd) {
        startPage();
        List<Dd> list = ddService.selectDdList(dd);
        return getDataTable(list);
    }

    /**
     * 参与名单
     */


    /**
     * 现场情况
     */

    /**
     * 收费明细查询
     */
    @GetMapping("/sfmxList")
    public AjaxResult sfmxList(DdSfmx ddSfmx) {
        List<DdSfmx> list = ddSfmxService.selectDdSfmxList(ddSfmx);
        Double zfy = 0d;
        for (DdSfmx sfmx : list) {
            zfy += Double.parseDouble(sfmx.getZj());
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("zfy", zfy);
        map.put("ddSfmxList", list);
        return success(map);
    }

    /**
     * 收费明细查询(实时)
     */
    @PostMapping("/sfmxSsList")
    public AjaxResult sfmxSs(@RequestBody Dd dd) {
        return success(ddSfmxService.selectDdSfmxSs(dd.getDdFwzqList(), dd.getDdSbyqList(), dd.getSfjj()));
    }


    /**
     * 修改订单
     */
    @NinanLog(title = "订单修改", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult update(@RequestBody Dd dd) {
        return toAjax(ddService.updateDd(dd));
    }


    /**
     * 平台招募查看剩余名额
     */
    @GetMapping("/selectCymdSyme")
    public AjaxResult update(Long ddid) {
        //测试支付成功
//        String sb="{\"mchid\":\"**********\",\"appid\":\"wx9b01ce8977d6dbe2\",\"out_trade_no\":\"1871843927007698944\",\"transaction_id\":\"4200002504202412253216654418\",\"trade_type\":\"JSAPI\",\"trade_state\":\"SUCCESS\",\"trade_state_desc\":\"支付成功\",\"bank_type\":\"OTHERS\",\"attach\":\"{\\\"isDeposit\\\":\\\"final\\\",\\\"ddbh\\\":\\\"618492396430299133\\\"}\",\"success_time\":\"2024-12-25T17:02:32+08:00\",\"payer\":{\"openid\":\"obDPo4rHSUd85-G771sx79JastTQ\"},\"amount\":{\"total\":10,\"payer_total\":10,\"currency\":\"CNY\",\"payer_currency\":\"CNY\"},\"type\":\"DBPay\"}";
//        rocketMQTemplate.asyncSend("RechargeSuccessful",sb, new SendCallback() {
//
//
//            @Override
//            public void onSuccess(SendResult sendResult) {
//                System.out.println("推送成功");
//            }
//
//            @Override
//            public void onException(Throwable throwable) {
//                System.out.println("推送失败");
//            }
//        });
        return success(ddCymdService.selectCymdSyme(ddid));
    }


    /**
     * 查询归属项目
     * @return
     */
    @GetMapping("/selectGsxm")
    public AjaxResult selectGsxm(){
        return success(ddService.selectGsxm());
    }

    /**
     * 删除当前订单接单人
     */
    @PostMapping("/deleteYjdr")
    public AjaxResult deleteYjdr(@RequestBody String jsonStr) {
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        // 提取参数
        Long ddid = jsonObject.getLong("ddid");
        List<Long> userIds = jsonObject.getJSONArray("userIds").toJavaList(Long.class);
        return success(ddCymdService.deleteYjdr(ddid, userIds));
    }


//    /**
//     * 队长接单接口
//     */
//    @GetMapping("/TeamLeaderOrderApi")
//    public AjaxResult TeamLeaderOrderApi (Long ddid){
//        // 队长接单相关方法  默认接ddid的全部
////        AjaxResult ajaxResult = ddCymdService.TeamLeaderOrderApi(ddid);
//        return success();
//    }

    /**
     * 队长保安员自动接单  自动根据缓存信息接单  没有队长接单那就是队长   有队长接单另一个队长接单就是保安员  保安员接单就是保安员
     */
    @GetMapping("/TeamMemberOrderApi")
    public AjaxResult TeamMemberOrderApi(Long ddid) {
        // 队员队长接单相关方法
        return ddCymdService.handleOrder(ddid);
    }

//    @Autowired
//    private JdbcTemplate jdbcTemplate;
//
//    @GetMapping("/txt")
//    public void txt(){
//        List<Map<String, Object>> maps = jdbcTemplate.queryForList("select ddbh,gzzj from `ry-vue`.`sys_dd`");
//        maps.forEach(map -> {
//           jdbcTemplate.update("update dd set gzzj=? where ddbh=?", map.get("gzzj"), map.get("ddbh"));
//        });
//
//    }

    /**
     * 导出订单列表
     */
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Dd dd) {
        List<Dd> list = ddService.selectDdList(dd);
        ExcelUtil<Dd> util = new ExcelUtil<Dd>(Dd.class);
        util.exportExcel(response, list, "订单数据");
    }

    /**
     * 删除订单
     */
    @RequiresPermissions("ninan:dd:remove")
    @Log(title = "订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(ddService.deleteDdByIds(ids));
    }
}
