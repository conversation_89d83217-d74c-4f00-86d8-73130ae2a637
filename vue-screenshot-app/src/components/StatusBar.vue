<template>
  <div class="status-bar">
    <span class="time">{{ currentTime }}</span>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'StatusBar',
  setup() {
    const currentTime = ref('12:00')
    let timeInterval = null
    
    const updateTime = () => {
      const now = new Date()
      const hours = now.getHours().toString().padStart(2, '0')
      const minutes = now.getMinutes().toString().padStart(2, '0')
      currentTime.value = `${hours}:${minutes}`
    }
    
    onMounted(() => {
      updateTime()
      // 每分钟更新一次时间
      timeInterval = setInterval(updateTime, 60000)
    })
    
    onUnmounted(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
    })
    
    return {
      currentTime
    }
  }
}
</script>

<style scoped>
.status-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 20px;
  background: white;
  font-size: 14px;
  font-weight: 600;
}
</style>
